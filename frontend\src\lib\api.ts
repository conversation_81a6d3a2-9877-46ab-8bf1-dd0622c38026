import axios from 'axios';

const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://127.0.0.1:8001/api',
});

export interface Agent {
  id: string;
  name: string;
  description: string;
  model_provider: string;
  config: any;
  created_at: string;
  updated_at: string;
}

export const getAgents = async (): Promise<Agent[]> => {
  const response = await apiClient.get('/agents');
  return response.data;
};

export const createAgent = async (agentData: Omit<Agent, 'id' | 'created_at' | 'updated_at'>) => {
  const response = await apiClient.post('/agents', agentData);
  return response.data;
};

export interface Workflow {
  id: string;
  agent_id: string;
  graph: any;
  version: number;
  status: string;
  created_at: string;
}

export const getWorkflows = async (): Promise<Workflow[]> => {
  const response = await apiClient.get('/workflows');
  return response.data;
}

export interface Run {
    id: string;
    workflow_id: string;
    status: string;
    started_at: string;
    finished_at: string | null;
    metrics: any;
}

export const createRun = async (runData: { workflow_id: string, inputs: any }): Promise<Run> => {
    const response = await apiClient.post('/runs', runData);
    return response.data;
}

export const getRun = async (runId: string): Promise<Run> => {
    const response = await apiClient.get(`/runs/${runId}`);
    return response.data;
}