import { NavLink, Outlet } from "react-router-dom";
import { BotMessageSquare, Workflow, PlaySquare, Settings, LayoutDashboard } from "lucide-react";

const Sidebar = () => (
  <aside className="w-64 bg-gray-50 border-r border-gray-200 flex flex-col">
    <div className="p-4 border-b border-gray-200">
      <h1 className="text-xl font-bold text-gray-800">Agent Framework</h1>
    </div>
    <nav className="flex-1 p-4 space-y-2">
      <SidebarLink to="/" icon={<LayoutDashboard size={20} />}>Dashboard</SidebarLink>
      <SidebarLink to="/agents" icon={<BotMessageSquare size={20} />}>Agents</SidebarLink>
      <SidebarLink to="/workflows" icon={<Workflow size={20} />}>Workflows</SidebarLink>
      <SidebarLink to="/runs" icon={<PlaySquare size={20} />}>Runs</SidebarLink>
      <SidebarLink to="/settings" icon={<Settings size={20} />}>Settings</SidebarLink>
    </nav>
  </aside>
);

import React from "react";

interface SidebarLinkProps {
  to: string;
  icon: React.ReactNode;
  children: React.ReactNode;
}

const SidebarLink: React.FC<SidebarLinkProps> = ({ to, icon, children }) => (
  <NavLink
    to={to}
    className={({ isActive }) =>
      `flex items-center px-3 py-2 text-gray-600 rounded-lg transition-colors duration-200 hover:bg-gray-200 ${
        isActive ? "bg-gray-200 text-gray-900 font-semibold" : "hover:text-gray-900"
      }`
    }
  >
    {icon}
    <span className="ml-3">{children}</span>
  </NavLink>
);

export const Layout = () => {
  return (
    <div className="flex h-screen bg-white">
      <Sidebar />
      <main className="flex-1 p-8 overflow-y-auto">
        <Outlet />
      </main>
    </div>
  );
};