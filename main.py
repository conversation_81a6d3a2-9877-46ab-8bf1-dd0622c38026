import json
from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from sqlmodel import Session, select

from app.core.database import create_db_and_tables, engine
from app.models.models import Agent, Workflow

def seed_database():
    """
    Seeds the database with a default agent and workflow if they don't exist.
    """
    with Session(engine) as session:
        # Check if the default agent already exists
        statement = select(Agent).where(Agent.name == "Simple Researcher")
        existing_agent = session.exec(statement).first()

        if not existing_agent:
            print("Seeding database with default agent and workflow...")
            # 1. Create the default agent
            researcher_agent = Agent(
                name="Simple Researcher",
                description="An agent that uses an LLM to plan, search the web, and synthesize information to answer a question.",
                model_provider="ollama",
                config={
                    "system_prompt": (
                        "You are a world-class research assistant. "
                        "Your goal is to answer the user's question based on a plan you create and the results of a web search. "
                        "1. **Planner**: First, create a step-by-step plan to answer the question. The only tool you have is a web search. Your plan should just be a search query. "
                        "2. **Searcher**: The web search tool will be called with your query. "
                        "3. **Synthesizer**: Finally, using the search results, provide a comprehensive answer to the original question."
                    )
                }
            )
            session.add(researcher_agent)
            session.commit()
            session.refresh(researcher_agent)

            # 2. Define the workflow graph
            researcher_workflow_graph = {
                "nodes": [
                    {
                        "id": "planner",
                        "type": "AgentNode",
                        "config": {
                            "system_prompt": "Based on the user's question: '{question}', what is the best search query to find the answer? Respond with only the search query and nothing else."
                        }
                    },
                    {
                        "id": "searcher",
                        "type": "ToolNode",
                        "config": {
                            "tool_name": "search_web"
                        }
                    },
                    {
                        "id": "synthesizer",
                        "type": "AgentNode",
                        "config": {
                            "system_prompt": "Based on the original question: '{question}' and the following search results: '{search_results}', provide a comprehensive answer."
                        }
                    }
                ],
                "edges": [
                    {"source": "planner", "target": "searcher"},
                    {"source": "searcher", "target": "synthesizer"},
                ],
                "entry_point": "planner"
            }

            # 3. Create the default workflow
            researcher_workflow = Workflow(
                agent_id=researcher_agent.id,
                graph=researcher_workflow_graph,
                status="published"
            )
            session.add(researcher_workflow)
            session.commit()
            print("Database seeding complete.")

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Code to run on startup
    print("Starting up...")
    create_db_and_tables()
    print("Database tables created.")
    seed_database()
    yield
    # Code to run on shutdown
    print("Shutting down...")

app = FastAPI(
    title="Web Agent Framework",
    description="A framework for building and managing agentic workflows.",
    version="0.1.0 (Prototype)",
    lifespan=lifespan
)

# CORS Middleware Configuration
origins = [
    "http://localhost:5173",
    "http://localhost:5174",
    "http://localhost:5175",
    "http://localhost:3000", # Common port for React dev servers
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/", tags=["Health Check"])
def read_root():
    """
    Root endpoint to check if the API is running.
    """
    return {"status": "ok", "message": "Welcome to the Web Agent Framework API!"}

# Include routers from the app.api module
from app.api import agents, workflows, runs
app.include_router(agents.router, prefix="/api")
app.include_router(workflows.router, prefix="/api")
app.include_router(runs.router, prefix="/api")
