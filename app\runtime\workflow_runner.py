import json
from langgraph.graph import StateGraph, <PERSON><PERSON>
from typing import TypedDict, Ann<PERSON>ted, List
import operator
from langchain_core.messages import AnyMessage, SystemMessage, HumanMessage, ToolMessage, AIMessage

from app.runtime import tools
from app.core.llm import get_llm

# 1. Define the state for our graph
class AgentState(TypedDict):
    """
    Represents the state of our agent.
    """
    messages: Annotated[List[AnyMessage], operator.add]


class WorkflowRunner:
    """
    Compiles and runs a workflow defined in JSON format using LangGraph.
    """
    def __init__(self, workflow_def: dict):
        self.workflow_def = workflow_def
        self.llm = get_llm().bind_tools([tools.search_web, tools.get_current_time])
        self.tool_registry = {
            "search_web": tools.search_web,
            "get_current_time": tools.get_current_time,
        }
        self.graph = self._compile()

    def _agent_node(self, state: AgentState):
        """A node that calls the LLM."""
        response = self.llm.invoke(state["messages"])
        return {"messages": [response]}

    def _tool_node(self, state: AgentState):
        """A node that executes tools based on the last AI message."""
        tool_messages = []
        last_message = state["messages"][-1]

        if not isinstance(last_message, AIMessage) or not last_message.tool_calls:
            # No tool calls, so we can end the graph here or handle it differently
            return {"messages": []}

        for tool_call in last_message.tool_calls:
            tool_name = tool_call["name"]
            if tool_name in self.tool_registry:
                tool_to_call = self.tool_registry[tool_name]
                # For simplicity, we assume args is a dict with a single 'query' key
                # A more robust implementation would handle different arg structures
                query = tool_call["args"].get("query", "")
                result = tool_to_call(query=query)
                tool_messages.append(ToolMessage(content=str(result), name=tool_name, tool_call_id=tool_call["id"]))
        
        return {"messages": tool_messages}

    def _router(self, state: AgentState):
        """A conditional edge that decides the next step."""
        last_message = state["messages"][-1]
        if isinstance(last_message, AIMessage) and last_message.tool_calls:
            return "tools"
        return "end"

    def _compile(self):
        """
        Compiles the JSON workflow definition into an executable LangGraph.
        """
        workflow = StateGraph(AgentState)
        
        # Dynamically add nodes from the workflow definition
        for node_def in self.workflow_def.get("nodes", []):
            node_name = node_def["id"]
            node_type = node_def["type"]
            if node_type == "AgentNode":
                workflow.add_node(node_name, self._agent_node)
            elif node_type == "ToolNode":
                workflow.add_node(node_name, self._tool_node)

        # Set the entry point
        entry_point = self.workflow_def.get("entry_point")
        if entry_point:
            workflow.set_entry_point(entry_point)

        # Add edges
        for edge_def in self.workflow_def.get("edges", []):
            source = edge_def["source"]
            target = edge_def["target"]
            
            # Handle conditional routing
            if edge_def.get("conditional"):
                workflow.add_conditional_edges(source, self._router, {"tools": target, "end": END})
            else:
                workflow.add_edge(source, target)

        return workflow.compile()

    async def run(self, inputs: dict):
        """
        Runs the compiled graph with the given inputs.
        """
        initial_messages = [
            SystemMessage(content=inputs.get("system_prompt", "You are a helpful assistant.")),
            HumanMessage(content=inputs.get("question", "Hello!"))
        ]
        final_state = None
        async for output in self.graph.astream({"messages": initial_messages}):
            final_state = output
        return final_state