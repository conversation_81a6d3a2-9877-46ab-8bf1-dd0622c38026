import React, { useState, useEffect, useCallback } from 'react';
import { getWorkflows, createRun, getRun } from '@/lib/api';
import type { Workflow, Run } from '@/lib/api';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { PlayCircle, Loader2, CheckCircle, XCircle } from 'lucide-react';

const RunsPage: React.FC = () => {
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [selectedWorkflowId, setSelectedWorkflowId] = useState<string | null>(null);
  const [runInput, setRunInput] = useState('{"question": "What is LangGraph?"}');
  const [activeRun, setActiveRun] = useState<Run | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const fetchWorkflows = async () => {
      try {
        const data = await getWorkflows();
        setWorkflows(data);
        if (data.length > 0) {
          setSelectedWorkflowId(data[0].id);
        }
      } catch (error) {
        console.error("Failed to fetch workflows:", error);
      }
    };
    fetchWorkflows();
  }, []);

  const handleRunWorkflow = async () => {
    if (!selectedWorkflowId) return;
    setIsLoading(true);
    setActiveRun(null);
    try {
      let inputJson = {};
      try {
        inputJson = JSON.parse(runInput);
      } catch {
        alert("Invalid JSON input.");
        setIsLoading(false);
        return;
      }
      const initialRun = await createRun({ workflow_id: selectedWorkflowId, inputs: inputJson });
      setActiveRun(initialRun);
    } catch (error) {
      console.error("Failed to start run:", error);
      alert("Failed to start run. See console for details.");
    } finally {
      setIsLoading(false);
    }
  };
  
  // Simple polling to update run status
  useEffect(() => {
    if (activeRun && (activeRun.status === 'running' || activeRun.status === 'pending')) {
      const interval = setInterval(async () => {
        try {
          const updatedRun = await getRun(activeRun.id);
          setActiveRun(updatedRun);
        } catch (error) {
          console.error("Failed to fetch run status:", error);
        }
      }, 2000);
      return () => clearInterval(interval);
    }
  }, [activeRun]);


  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold">Run Monitor</h1>
        <p className="mt-2 text-lg text-gray-600">Trigger workflows and monitor their execution in real-time.</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div className="md:col-span-1 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>1. Select Workflow</CardTitle>
            </CardHeader>
            <CardContent>
              <select
                value={selectedWorkflowId ?? ''}
                onChange={(e) => setSelectedWorkflowId(e.target.value)}
                className="w-full p-2 border rounded-md"
              >
                <option value="" disabled>Select a workflow</option>
                {workflows.map(wf => (
                  <option key={wf.id} value={wf.id}>{wf.id}</option>
                ))}
              </select>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>2. Provide Input</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                value={runInput}
                onChange={(e) => setRunInput(e.target.value)}
                rows={5}
                placeholder='Enter JSON input for the workflow'
              />
            </CardContent>
          </Card>
          <Button onClick={handleRunWorkflow} disabled={!selectedWorkflowId || isLoading} className="w-full">
            {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <PlayCircle className="mr-2 h-4 w-4" />}
            {isLoading ? 'Running...' : 'Run Workflow'}
          </Button>
        </div>

        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Run Status</CardTitle>
              <CardDescription>Details of the active or last run.</CardDescription>
            </CardHeader>
            <CardContent>
              {activeRun ? (
                <div className="space-y-4">
                  <div className="flex items-center">
                    {activeRun.status === 'completed' && <CheckCircle className="w-5 h-5 text-green-500 mr-2" />}
                    {activeRun.status === 'failed' && <XCircle className="w-5 h-5 text-red-500 mr-2" />}
                    {(activeRun.status === 'running' || activeRun.status === 'pending') && <Loader2 className="w-5 h-5 animate-spin mr-2" />}
                    <span className="font-semibold capitalize">{activeRun.status}</span>
                  </div>
                  <div><strong>Run ID:</strong> {activeRun.id}</div>
                  <div><strong>Workflow ID:</strong> {activeRun.workflow_id}</div>
                  <div><strong>Started:</strong> {new Date(activeRun.started_at).toLocaleString()}</div>
                  {activeRun.finished_at && <div><strong>Finished:</strong> {new Date(activeRun.finished_at).toLocaleString()}</div>}
                  <div className="mt-4">
                    <h3 className="font-semibold">Metrics / Output:</h3>
                    <pre className="text-sm bg-gray-100 dark:bg-gray-800 p-4 rounded-md mt-2">
                      {JSON.stringify(activeRun.metrics, null, 2) || 'No output yet.'}
                    </pre>
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-40 bg-gray-100 dark:bg-gray-800/50 rounded-md">
                  <p className="text-muted-foreground">No active run. Trigger a workflow to begin.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default RunsPage;