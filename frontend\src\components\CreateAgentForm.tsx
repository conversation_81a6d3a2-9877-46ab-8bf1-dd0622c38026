import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { createAgent } from '@/lib/api';

interface CreateAgentFormProps {
  onAgentCreated: () => void;
}

export const CreateAgentForm: React.FC<CreateAgentFormProps> = ({ onAgentCreated }) => {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [systemPrompt, setSystemPrompt] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await createAgent({
        name,
        description,
        model_provider: 'ollama', // Default value
        config: {
          system_prompt: systemPrompt,
          temperature: 0.1,
        },
      });
      setName('');
      setDescription('');
      setSystemPrompt('');
      onAgentCreated();
    } catch (error) {
      console.error("Failed to create agent:", error);
      // TODO: Show error to user
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Create New Agent</CardTitle>
        <CardDescription>Define a new agent to be used in workflows.</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700">Name</label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="e.g., cyber-defender"
              required
            />
          </div>
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700">Description</label>
            <Input
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="A brief description of the agent's purpose."
              required
            />
          </div>
          <div>
            <label htmlFor="systemPrompt" className="block text-sm font-medium text-gray-700">System Prompt</label>
            <Textarea
              id="systemPrompt"
              value={systemPrompt}
              onChange={(e) => setSystemPrompt(e.target.value)}
              placeholder="You are a helpful assistant..."
              required
              rows={4}
            />
          </div>
          <Button type="submit">Create Agent</Button>
        </form>
      </CardContent>
    </Card>
  );
};