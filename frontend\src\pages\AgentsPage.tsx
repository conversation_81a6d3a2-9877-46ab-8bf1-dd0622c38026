import React, { useState, useEffect, useCallback } from 'react';
import { getAgents } from '@/lib/api';
import type { Agent } from '@/lib/api';
import { CreateAgentForm } from '@/components/CreateAgentForm';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { BotMessageSquare } from 'lucide-react';

const AgentList: React.FC<{ agents: Agent[] }> = ({ agents }) => (
  <Card>
    <CardHeader>
      <CardTitle>Available Agents</CardTitle>
      <CardDescription>List of all configured agents.</CardDescription>
    </CardHeader>
    <CardContent>
      <div className="space-y-4">
        {agents.length > 0 ? (
          agents.map(agent => (
            <div key={agent.id} className="p-4 border rounded-lg flex items-start space-x-4">
              <BotMessageSquare className="w-6 h-6 text-gray-500 mt-1" />
              <div className="flex-1">
                <p className="font-semibold text-lg">{agent.name}</p>
                <p className="text-sm text-gray-600">{agent.description}</p>
                <div className="text-xs text-gray-400 mt-2">
                  <span>Provider: {agent.model_provider}</span> | <span>ID: {agent.id}</span>
                </div>
              </div>
            </div>
          ))
        ) : (
          <p className="text-center text-gray-500">No agents found. Create one to get started.</p>
        )}
      </div>
    </CardContent>
  </Card>
);


const AgentsPage: React.FC = () => {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [refreshSignal, setRefreshSignal] = useState(0);

  const fetchAgents = useCallback(async () => {
    try {
      const data = await getAgents();
      setAgents(data);
    } catch (error) {
      console.error("Failed to fetch agents:", error);
      // TODO: Show error to user
    }
  }, []);

  useEffect(() => {
    fetchAgents();
  }, [fetchAgents, refreshSignal]);

  const handleAgentCreated = () => {
    setRefreshSignal(prev => prev + 1);
  };

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold">Agent Management</h1>
        <p className="mt-2 text-lg text-gray-600">Create, view, and manage your intelligent agents.</p>
      </div>
      <CreateAgentForm onAgentCreated={handleAgentCreated} />
      <AgentList agents={agents} />
    </div>
  );
};

export default AgentsPage;