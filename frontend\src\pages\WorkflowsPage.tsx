import React, { useState, useEffect } from 'react';
import { getWorkflows } from '@/lib/api';
import type { Workflow } from '@/lib/api';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Workflow as WorkflowIcon } from 'lucide-react';

const WorkflowsPage: React.FC = () => {
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchWorkflows = async () => {
      try {
        const data = await getWorkflows();
        setWorkflows(data);
      } catch (error) {
        console.error("Failed to fetch workflows:", error);
        // TODO: Show error to user
      } finally {
        setLoading(false);
      }
    };

    fetchWorkflows();
  }, []);

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold">Workflows</h1>
        <p className="mt-2 text-lg text-gray-600">Browse and manage your workflows.</p>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Available Workflows</CardTitle>
          <CardDescription>List of all registered workflows.</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p>Loading workflows...</p>
          ) : (
            <div className="space-y-4">
              {workflows.length > 0 ? (
                workflows.map(workflow => (
                  <div key={workflow.id} className="p-4 border rounded-lg flex items-start space-x-4">
                    <WorkflowIcon className="w-6 h-6 text-gray-500 mt-1" />
                    <div className="flex-1">
                      <p className="font-semibold text-lg">Workflow ID: {workflow.id}</p>
                      <p className="text-sm text-gray-600">Agent ID: {workflow.agent_id}</p>
                      <pre className="text-xs text-gray-400 bg-gray-100 p-2 rounded-md mt-2">
                        {JSON.stringify(workflow.graph, null, 2)}
                      </pre>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-center text-gray-500">No workflows found.</p>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default WorkflowsPage;