from datetime import datetime

def get_current_time() -> str:
    """
    Returns the current date and time as a formatted string.
    This serves as a simple example of a tool that can be called by an agent.
    """
    return datetime.now().isoformat()

def search_web(query: str) -> str:
    """
    A mock web search tool that returns a fixed result for a given query.
    """
    print(f"--- Searching web for: {query} ---")
    return f"LangGraph is a library for building stateful, multi-actor applications with LLMs. It extends the LangChain expression language with the ability to coordinate multiple chains (or actors) across multiple steps of computation in a cyclic manner. It is inspired by <PERSON><PERSON> and Apache Beam."

# In the future, more complex tools can be added here.